package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.RandomUtil;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHuBeiShuKePayChannel;
import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeEduManager;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduOrderConfirmReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 湖北数科教育包支付渠道测试类
 *
 * 测试说明：
 * 1. testGetSmsCode() - 基础测试，使用随机验证码
 * 2. testGetSmsCodeWithRealCode() - 真实测试，需要手动输入收到的短信验证码
 * 3. testProxyFunction() - 代理IP功能测试
 *
 * 注意事项：
 * - 测试前请确保手机号为电信号码
 * - 真实测试时需要修改代码中的验证码
 * - 代理IP功能会自动获取新的代理进行测试
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class BenefitHuBeiShuKePayChannelTest {


    @Autowired
    private BenefitHuBeiShuKePayChannel benefitHuBeiShuKePayChannel;

    @Test
    public void testGetSmsCode() throws InterruptedException {
        // 测试手机号（写死）
        String mobile = "15347232370";

        // 创建用户请求对象
        UserBenefitOrderReq userReq = new UserBenefitOrderReq();
        userReq.setMobile(mobile);
        userReq.setProdCode("EDU_BY_THUMB"); // 写死产品编码
        userReq.setChannelId(1L); // 写死渠道ID

        // 创建产品对象
        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setPayChannelPkgId(HuBeiShuKePayPkgEnum.EDU_BY_THUMB.getChannelPkgId()); // 写死包类型
        productDO.setProdId(1L); // 写死产品ID
        productDO.setProdName("教育包测试"); // 写死产品名称

        log.info("=== 开始测试获取短信验证码 ===");
        log.info("测试手机号: {}", mobile);

        // 1. 发送短信验证码
        try {
            boolean sendResult = benefitHuBeiShuKePayChannel.sendEduSmsCode(userReq, productDO);
            log.info("发送短信验证码结果: {}", sendResult ? "成功" : "失败");

            if (!sendResult) {
                log.error("发送短信验证码失败，测试终止");
                return;
            }
        } catch (Exception e) {
            log.error("发送短信验证码异常", e);
            return;
        }

        log.info("=== 等待输入验证码 ===");
        log.info("请查看手机短信，或者使用随机生成的验证码进行测试");
        Thread.sleep(5000);

        // 2. 模拟用户输入验证码（这里使用随机生成的验证码进行测试）
        String smsCode = RandomUtil.randomNumbers(6);
        log.info("使用随机生成的验证码进行测试: {}", smsCode);

        // 设置验证码到请求对象
        userReq.setSmsCode(smsCode);

        // 创建订单对象
        BenefitOrderDO orderDO = new BenefitOrderDO();
        orderDO.setOrderNo("TEST_ORDER_" + System.currentTimeMillis()); // 写死订单号
        orderDO.setPhone(mobile);
        orderDO.setProdId(productDO.getProdId());

        log.info("=== 开始测试验证短信验证码 ===");

        // 3. 验证短信验证码
        try {
            BenefitPayResultBO verifyResult = benefitHuBeiShuKePayChannel.verifyEduSmsCode(userReq, productDO, orderDO);
            log.info("验证短信验证码结果: {}", verifyResult);

            if (verifyResult.isSuccess()) {
                log.info("✅ 测试成功！订单处理完成");
            } else {
                log.warn("⚠️ 验证失败: {}", verifyResult.getMessage());
            }
        } catch (Exception e) {
            log.error("验证短信验证码异常", e);
        }

        log.info("=== 测试完成 ===");
    }



}
