package com.yuelan.hermes.quanyi.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Excel转JSON工具类
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
public class ExcelToJsonUtil {

    /**
     * 读取Excel文件并转换为JSON列表
     * 
     * @param filePath Excel文件路径
     * @return JSON对象列表
     */
    public static List<JSONObject> readExcelToJson(String filePath) {
        return readExcelToJson(filePath, new String[]{"订购页订单号", "订购类型", "状态", "描述", "手机号"});
    }

    /**
     * 读取Excel文件并转换为JSON列表
     * 
     * @param filePath Excel文件路径
     * @param expectedHeaders 期望的表头数组
     * @return JSON对象列表
     */
    public static List<JSONObject> readExcelToJson(String filePath, String[] expectedHeaders) {
        File file = new File(filePath);
        
        if (!file.exists()) {
            log.error("文件不存在: {}", filePath);
            return new ArrayList<>();
        }
        
        List<JSONObject> jsonList = new ArrayList<>();
        
        try {
            EasyExcel.read(file, new AnalysisEventListener<Map<Integer, String>>() {
                
                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    // 跳过表头
                    if (context.readRowHolder().getRowIndex() == 0) {
                        return;
                    }
                    
                    JSONObject jsonObject = convertRowToJson(rowData, expectedHeaders);
                    jsonObject.put("rowIndex", context.readRowHolder().getRowIndex() + 1);
                    
                    jsonList.add(jsonObject);
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共处理 {} 行数据", jsonList.size());
                }
                
            }).sheet().doRead();
            
        } catch (Exception e) {
            log.error("读取Excel文件异常: {}", filePath, e);
        }
        
        return jsonList;
    }

    /**
     * 将行数据转换为JSON对象
     */
    private static JSONObject convertRowToJson(Map<Integer, String> rowData, String[] headers) {
        JSONObject jsonObject = new JSONObject();
        
        // 默认字段映射
        String[] fieldNames = {"orderNo", "orderType", "status", "description", "mobile"};
        
        for (int i = 0; i < Math.min(headers.length, fieldNames.length); i++) {
            String value = rowData.get(i);
            jsonObject.put(fieldNames[i], value != null ? value.trim() : "");
        }
        
        return jsonObject;
    }

    /**
     * 打印JSON列表
     */
    public static void printJsonList(List<JSONObject> jsonList) {
        log.info("=== 开始输出JSON数据 ===");
        for (int i = 0; i < jsonList.size(); i++) {
            JSONObject json = jsonList.get(i);
            log.info("第{}行: {}", i + 1, json.toJSONString());
        }
        log.info("=== JSON数据输出完成 ===");
    }

    /**
     * 将JSON列表保存到文件
     */
    public static void saveJsonToFile(List<JSONObject> jsonList, String outputPath) {
        try {
            String jsonString = JSON.toJSONString(jsonList, true);
            java.nio.file.Files.write(java.nio.file.Paths.get(outputPath), jsonString.getBytes());
            log.info("JSON数据已保存到文件: {}", outputPath);
        } catch (Exception e) {
            log.error("保存JSON文件失败: {}", outputPath, e);
        }
    }

    /**
     * 快速转换方法 - 直接输出到控制台
     */
    public static void quickConvert(String filePath) {
        log.info("开始快速转换Excel到JSON: {}", filePath);
        
        List<JSONObject> jsonList = readExcelToJson(filePath);
        
        if (jsonList.isEmpty()) {
            log.warn("没有读取到数据");
            return;
        }
        
        // 输出每行JSON
        printJsonList(jsonList);
        
        // 输出统计信息
        printStatistics(jsonList);
    }

    /**
     * 打印统计信息
     */
    private static void printStatistics(List<JSONObject> jsonList) {
        log.info("=== 统计信息 ===");
        log.info("总行数: {}", jsonList.size());
        
        // 统计状态
        Map<String, Long> statusCount = jsonList.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    json -> json.getString("status"), 
                    java.util.stream.Collectors.counting()
                ));
        log.info("状态统计: {}", statusCount);
        
        // 统计订购类型
        Map<String, Long> orderTypeCount = jsonList.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    json -> json.getString("orderType"), 
                    java.util.stream.Collectors.counting()
                ));
        log.info("订购类型统计: {}", orderTypeCount);
    }

    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        String filePath = "/Users/<USER>/Downloads/西米全量订单.xlsx";
        
        // 快速转换
        quickConvert(filePath);
        
        // 或者获取JSON列表进行自定义处理
        // List<JSONObject> jsonList = readExcelToJson(filePath);
        // 自定义处理逻辑...
    }
}
