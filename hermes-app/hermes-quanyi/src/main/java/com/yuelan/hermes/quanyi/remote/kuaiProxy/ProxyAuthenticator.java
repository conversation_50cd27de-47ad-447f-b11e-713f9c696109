package com.yuelan.hermes.quanyi.remote.kuaiProxy;

import java.net.Authenticator;
import java.net.PasswordAuthentication;

/**
 * 代理验证信息
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
public class ProxyAuthenticator extends Authenticator {
    private String user;
    private String password;

    public ProxyAuthenticator(String user, String password) {
        this.user = user;
        this.password = password;
    }

    @Override
    protected PasswordAuthentication getPasswordAuthentication() {
        return new PasswordAuthentication(user, password.toCharArray());
    }
}
