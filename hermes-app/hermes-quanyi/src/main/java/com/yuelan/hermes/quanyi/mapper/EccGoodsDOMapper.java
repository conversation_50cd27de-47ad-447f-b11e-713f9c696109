package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.hermes.quanyi.controller.request.EccGoodsListReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> 2024/5/2 上午12:03
 */
public interface EccGoodsDOMapper extends MPJBaseMapper<EccGoodsDO> {


    IPage<EccGoodsDO> selectPageExpend(IPage<EccGoodsDO> page, @Param("req") EccGoodsListReq req);

    List<EccGoodsDO> selectExpendAndStock(@Param("ids") List<Long> goodsIds);


    EccGoodsDO selectDetailById(@Param("goodsId") Long goodsId);
}