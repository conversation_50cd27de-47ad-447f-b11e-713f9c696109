package com.yuelan.hermes.quanyi.biz.handler.impl;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.proxy.KuaiProxyHttpRequestSetter;
import com.yuelan.hermes.quanyi.common.proxy.ProxyHttpRequestSetter;
import com.yuelan.hermes.quanyi.remote.hbsk.response.ActivityInfoResp;
import com.yuelan.hermes.quanyi.remote.kuaiProxy.KuaiProxyManager;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 教育包缓存策略测试类
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EduCacheStrategyTest {

    private final KuaiProxyManager kuaiProxyManager;

    /**
     * 测试新的缓存策略
     */
    public void testNewCacheStrategy(String mobile) {
        log.info("=== 测试新的教育包缓存策略 ===");
        
        try {
            // 1. 模拟获取验证码流程 - 获取新的代理IP
            String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort();
            String[] parts = proxyIpAndPort.split(":");
            String proxyIp = parts[0].trim();
            int proxyPort = Integer.parseInt(parts[1].trim());
            
            log.info("步骤1: 获取新的代理IP: {}:{}", proxyIp, proxyPort);
            
            // 2. 模拟活动信息响应
            ActivityInfoResp mockActivityInfo = new ActivityInfoResp();
            mockActivityInfo.setCode(200);
            mockActivityInfo.setText("success");
            // 这里应该设置真实的活动信息，为了测试简化
            
            // 3. 缓存活动信息和代理设置
            cacheEduData(mobile, mockActivityInfo, proxyIp, proxyPort);
            log.info("步骤2: 缓存活动信息和代理设置完成");
            
            // 4. 模拟提交验证码流程 - 从缓存中获取数据
            EduCacheData cacheData = getCachedEduData(mobile);
            if (cacheData != null) {
                log.info("步骤3: 成功从缓存获取数据");
                log.info("  - 活动信息: {}", cacheData.getActivityInfoResp().getText());
                log.info("  - 代理IP: {}:{}", cacheData.getProxyIp(), cacheData.getProxyPort());
                
                // 5. 创建代理设置器
                if (cacheData.hasProxy()) {
                    ProxyHttpRequestSetter proxySetter = KuaiProxyHttpRequestSetter.create(
                        kuaiProxyManager, cacheData.getProxyIp(), cacheData.getProxyPort());
                    log.info("步骤4: 成功创建代理设置器");
                    
                    // 6. 测试代理设置器
                    testProxySetter(proxySetter);
                }
            } else {
                log.error("步骤3: 从缓存获取数据失败");
            }
            
            // 7. 测试缓存过期
            log.info("步骤5: 等待缓存过期测试...");
            // 在实际测试中，可以修改过期时间为几秒钟进行测试
            
        } catch (Exception e) {
            log.error("测试新缓存策略失败", e);
        }
    }

    /**
     * 测试缓存过期场景
     */
    public void testCacheExpiration(String mobile) {
        log.info("=== 测试缓存过期场景 ===");
        
        try {
            // 1. 先检查是否有缓存
            EduCacheData cacheData = getCachedEduData(mobile);
            if (cacheData != null) {
                log.info("发现缓存数据，代理IP: {}:{}", cacheData.getProxyIp(), cacheData.getProxyPort());
            } else {
                log.info("没有缓存数据，模拟验证码已过期场景");
            }
            
            // 2. 清除缓存（模拟过期）
            clearCache(mobile);
            log.info("清除缓存完成");
            
            // 3. 再次检查缓存
            cacheData = getCachedEduData(mobile);
            if (cacheData == null) {
                log.info("缓存已清除，验证码过期场景测试成功");
            } else {
                log.error("缓存清除失败");
            }
            
        } catch (Exception e) {
            log.error("测试缓存过期失败", e);
        }
    }

    /**
     * 测试多用户缓存隔离
     */
    public void testMultiUserCacheIsolation() {
        log.info("=== 测试多用户缓存隔离 ===");
        
        try {
            String mobile1 = "13800138001";
            String mobile2 = "13800138002";
            
            // 为两个用户分别获取不同的代理IP
            String[] proxy1 = getNewProxy();
            String[] proxy2 = getNewProxy();
            
            // 创建模拟活动信息
            ActivityInfoResp activity1 = createMockActivity("活动1");
            ActivityInfoResp activity2 = createMockActivity("活动2");
            
            // 分别缓存
            cacheEduData(mobile1, activity1, proxy1[0], Integer.parseInt(proxy1[1]));
            cacheEduData(mobile2, activity2, proxy2[0], Integer.parseInt(proxy2[1]));
            
            log.info("为两个用户分别缓存数据完成");
            
            // 验证缓存隔离
            EduCacheData cache1 = getCachedEduData(mobile1);
            EduCacheData cache2 = getCachedEduData(mobile2);
            
            if (cache1 != null && cache2 != null) {
                log.info("用户1缓存: 活动={}, 代理={}:{}", 
                    cache1.getActivityInfoResp().getText(), cache1.getProxyIp(), cache1.getProxyPort());
                log.info("用户2缓存: 活动={}, 代理={}:{}", 
                    cache2.getActivityInfoResp().getText(), cache2.getProxyIp(), cache2.getProxyPort());
                
                if (!cache1.getProxyIp().equals(cache2.getProxyIp())) {
                    log.info("多用户缓存隔离测试成功");
                } else {
                    log.warn("两个用户使用了相同的代理IP，可能是巧合");
                }
            } else {
                log.error("多用户缓存测试失败");
            }
            
        } catch (Exception e) {
            log.error("测试多用户缓存隔离失败", e);
        }
    }

    // 辅助方法
    private void cacheEduData(String mobile, ActivityInfoResp activityInfo, String proxyIp, int proxyPort) {
        String cacheKey = RedisKeys.getBenefitNumber("HBSK_EDU", mobile);
        EduCacheData cacheData = EduCacheData.create(activityInfo, proxyIp, proxyPort);
        String cacheVal = JSONObject.toJSONString(cacheData);
        RedisUtils.setCacheObject(cacheKey, cacheVal, Duration.ofMinutes(8));
    }

    private EduCacheData getCachedEduData(String mobile) {
        String cacheKey = RedisKeys.getBenefitNumber("HBSK_EDU", mobile);
        Object cacheObject = RedisUtils.getCacheObject(cacheKey);
        if (cacheObject != null) {
            return JSONObject.parseObject(cacheObject.toString(), EduCacheData.class);
        }
        return null;
    }

    private void clearCache(String mobile) {
        String cacheKey = RedisKeys.getBenefitNumber("HBSK_EDU", mobile);
        RedisUtils.deleteObject(cacheKey);
    }

    private String[] getNewProxy() throws Exception {
        String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort();
        String[] parts = proxyIpAndPort.split(":");
        return new String[]{parts[0].trim(), parts[1].trim()};
    }

    private ActivityInfoResp createMockActivity(String name) {
        ActivityInfoResp activity = new ActivityInfoResp();
        activity.setCode(200);
        activity.setText(name);
        return activity;
    }

    private void testProxySetter(ProxyHttpRequestSetter proxySetter) {
        log.info("测试代理设置器功能...");
        // 这里可以添加实际的HTTP请求测试
        log.info("代理设置器测试完成");
    }

    /**
     * 运行所有测试
     */
    public void runAllTests(String mobile) {
        log.info("开始运行教育包缓存策略测试...");
        
        testNewCacheStrategy(mobile);
        testCacheExpiration(mobile);
        testMultiUserCacheIsolation();
        
        log.info("教育包缓存策略测试完成");
    }
}
