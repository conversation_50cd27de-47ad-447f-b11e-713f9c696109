package com.yuelan.hermes.quanyi.remote.kuaiProxy;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.yuelan.hermes.quanyi.common.pojo.properties.KuaiProxyProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.Authenticator;
import java.net.HttpURLConnection;
import java.net.PasswordAuthentication;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

/**
 * 快代理管理器
 *
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KuaiProxyManager {

    private final KuaiProxyProperties kuaiProxyProperties;



    /**
     * 获取代理IP和端口 - 使用HTTP请求方式（默认获取1个）
     *
     * @return 代理IP和端口，格式：ip:port
     * @throws Exception 获取异常
     */
    public String getProxyIpAndPort() throws Exception {
        return getProxyIpAndPort(1, new HashMap<>());
    }

    /**
     * 获取代理IP和端口 - 使用HTTP请求方式，可指定获取数量
     *
     * @param num 获取代理IP的数量
     * @param data 可选的额外数据参数
     * @return 代理IP和端口列表，多个时用换行分隔
     * @throws Exception 获取异常
     */
    public String getProxyIpAndPort(int num, Map<String, Object> data) throws Exception {
        try {
            String urlString = kuaiProxyProperties.getProxyUrl(num);
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");

            // 读取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                String inputLine;
                StringBuilder response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

                log.info("获取{}个代理IP响应: {}", num, response.toString());
                return response.toString().trim();
            } else {
                throw new Exception("获取代理IP失败，响应码: " + responseCode);
            }
        } catch (IOException e) {
            log.error("获取代理ip和端口异常", e);
            throw e;
        }
    }

    /**
     * 为HttpRequest对象设置代理
     *
     * @param proxyIp 代理IP
     * @param proxyPort 代理端口
     * @param httpRequest HttpRequest对象
     * @return 设置了代理的HttpRequest对象
     */
    public HttpRequest setProxyForHttpRequest(String proxyIp, int proxyPort, HttpRequest httpRequest) {
        // JDK 8u111版本后，目标页面为HTTPS协议，启用proxy用户密码鉴权
        System.setProperty("jdk.http.auth.tunneling.disabledSchemes", "");

        // 设置请求验证信息
        Authenticator.setDefault(new ProxyAuthenticator(
            kuaiProxyProperties.getProxyUser(),
            kuaiProxyProperties.getProxyPass()
        ));

        // 为HttpRequest设置代理
        return httpRequest.setHttpProxy(proxyIp, proxyPort);
    }

    /**
     * 测试代理连接
     *
     * @param proxyIp 代理IP
     * @param proxyPort 代理端口
     * @return 测试结果
     */
    public String testProxyConnection(String proxyIp, int proxyPort) {
        log.info("测试代理连接 - IP: {}, Port: {}", proxyIp, proxyPort);

        // 设置代理认证
        System.setProperty("jdk.http.auth.tunneling.disabledSchemes", "");
        Authenticator.setDefault(new ProxyAuthenticator(
            kuaiProxyProperties.getProxyUser(),
            kuaiProxyProperties.getProxyPass()
        ));

        // 发送测试请求
        HttpResponse result = HttpRequest.get(kuaiProxyProperties.getPageUrl())
                .setHttpProxy(proxyIp, proxyPort)
                .timeout(20000) // 设置超时，毫秒
                .execute();

        String responseBody = result.body();
        log.info("代理测试响应: {}", responseBody);
        return responseBody;
    }

    /**
     * 代理验证信息
     *
     * <AUTHOR> 2025/8/13
     * @since 2025/8/13
     */
    public static class ProxyAuthenticator extends Authenticator {
        private String user;
        private String password;

        public ProxyAuthenticator(String user, String password) {
            this.user = user;
            this.password = password;
        }

        @Override
        protected PasswordAuthentication getPasswordAuthentication() {
            return new PasswordAuthentication(user, password.toCharArray());
        }
    }
}
