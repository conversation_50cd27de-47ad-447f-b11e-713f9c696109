package com.yuelan.hermes.quanyi.biz.handler.impl;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.quanyi.biz.handler.BenefitPayHandler;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.biz.service.HttpAsyncTaskService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.SuccessStrategyEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.HttpAsyncTask;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.config.task.HttpTaskRequest;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeEduManager;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeMusicManager;
import com.yuelan.hermes.quanyi.remote.kuaiProxy.KuaiProxyManager;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduOrderConfirmReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.ShuKeTwReportReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.*;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 2025/4/29
 * @since 2025/4/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BenefitHuBeiShuKePayChannel implements BenefitPayHandler {

    public static final String BUSINESS_TYPE = "SHUKE_EDU";

    private final HuBeiShuKeMusicManager musicManager;
    private final HuBeiShuKeEduManager eduManager;
    private final HuBeiShuKeEduProperties huBeiShuKeEduProperties;
    private final HttpAsyncTaskService httpAsyncTaskService;
    private final KuaiProxyManager kuaiProxyManager;
    @Resource
    @Lazy
    BenefitPlatformService benefitPlatformService;


    @Override
    public BenefitPayChannelEnum getChannel() {
        return BenefitPayChannelEnum.HB_SHU_KE;
    }

    /**
     * rsa公钥加密
     *
     * @param data      待加密数据
     * @param publicKey 公钥
     * @return 加密后字符串, 采用Base64编码
     */
    private static String encryptByRsa(String data, String publicKey) {
        RSA rsa = SecureUtil.rsa(null, publicKey);
        return rsa.encryptBase64(data, StandardCharsets.UTF_8, KeyType.PublicKey);
    }

    @Override
    public BenefitPayResultBO getPayUrl(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        String mobile = req.getMobile();
        String smsCode = req.getSmsCode();
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKePayPkgEnum.PkgType pkgType = payPkgEnum.getPkgType();
        if (Objects.isNull(smsCode)) {
            log.error("短信验证码不能为空，手机号: {},-请重新提交验证码", mobile);
            return BenefitPayResultBO.fail("短信验证码不能为空");
        }
        String spOrderNo = RedisUtils.getCacheObject(cacheKey(mobile));
        if (Objects.isNull(spOrderNo)) {
            log.error("短信验证码已失效，手机号: {},-请重新获取验证码", mobile);
            return BenefitPayResultBO.fail("短信验证码失效，请重新获取验证码");
        }
        BenefitPayResultBO resultBO = null;
        try {
            if (HuBeiShuKePayPkgEnum.PkgType.EDU.equals(pkgType)) {
                return verifyEduSmsCode(req, productDO, orderDO);
            }
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "暂时只支持教育包订购");
        } catch (Exception e) {
            log.error("验证验证码异常，手机号: {},-请重新获取验证码", mobile, e);
            resultBO = BenefitPayResultBO.fail("验证验证码异常");
        } finally {
            if (Objects.nonNull(resultBO) && resultBO.isSuccess()) {
                RedisUtils.deleteObject(cacheKey(mobile));
            }
        }
        return resultBO;
    }

    private String cacheKey(String mobile) {
        return RedisKeys.getBenefitNumber(getChannel(), mobile);
    }

    /**
     * 获取用户代理IP缓存key
     */
    private String proxyIpCacheKey(String mobile) {
        return RedisKeys.getBenefitNumber(getChannel() + "_PROXY", mobile);
    }

    /**
     * 为用户获取并缓存代理IP
     *
     * @param mobile 用户手机号
     * @return 代理IP和端口数组 [ip, port]
     */
    private String[] getOrCreateProxyForUser(String mobile) {
        String cacheKey = proxyIpCacheKey(mobile);
        String cachedProxy = RedisUtils.getCacheObject(cacheKey);

        if (cachedProxy != null) {
            log.info("用户 {} 复用缓存的代理IP: {}", mobile, cachedProxy);
            String[] parts = cachedProxy.split(":");
            if (parts.length >= 2) {
                return new String[]{parts[0].trim(), parts[1].trim()};
            }
        }

        try {
            // 获取新的代理IP
            String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort();
            log.info("为用户 {} 获取新的代理IP: {}", mobile, proxyIpAndPort);

            String[] parts = proxyIpAndPort.split(":");
            if (parts.length < 2) {
                throw new RuntimeException("代理IP格式错误: " + proxyIpAndPort);
            }

            // 缓存代理IP，设置15分钟过期（与短信验证码有效期一致）
            RedisUtils.setCacheObject(cacheKey, proxyIpAndPort, Duration.ofMinutes(15));

            return new String[]{parts[0].trim(), parts[1].trim()};
        } catch (Exception e) {
            log.error("为用户 {} 获取代理IP失败", mobile, e);
            throw new RuntimeException("获取代理IP失败", e);
        }
    }

    /**
     * 获取用户缓存的代理IP
     *
     * @param mobile 用户手机号
     * @return 代理IP和端口数组 [ip, port]，如果没有缓存则返回null
     */
    private String[] getCachedProxyForUser(String mobile) {
        String cacheKey = proxyIpCacheKey(mobile);
        String cachedProxy = RedisUtils.getCacheObject(cacheKey);

        if (cachedProxy != null) {
            String[] parts = cachedProxy.split(":");
            if (parts.length >= 2) {
                return new String[]{parts[0].trim(), parts[1].trim()};
            }
        }
        return null;
    }

    /**
     * 清除用户的代理IP缓存
     *
     * @param mobile 用户手机号
     */
    private void clearProxyForUser(String mobile) {
        String cacheKey = proxyIpCacheKey(mobile);
        RedisUtils.deleteObject(cacheKey);
        log.info("清除用户 {} 的代理IP缓存", mobile);
    }


    public HuBeiShiKeCallBackResp dealCallBack(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        log.info("收到回调参数: {}", JSON.toJSONString(parameterMap));
        return HuBeiShiKeCallBackResp.success();
    }

    @Override
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKePayPkgEnum.PkgType pkgType = payPkgEnum.getPkgType();
        if (HuBeiShuKePayPkgEnum.PkgType.EDU.equals(pkgType)) {
            return sendEduSmsCode(reqParams, productDO);
        }
        throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "暂时只支持教育包订购");
    }

    /**
     * 教育包获取验证码
     */
    public boolean sendEduSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(payPkgEnum);

        // 获取用户的代理IP
        String[] proxyInfo = getOrCreateProxyForUser(mobile);
        String proxyIp = proxyInfo[0];
        int proxyPort = Integer.parseInt(proxyInfo[1]);

        // 使用代理IP获取活动信息
        ActivityInfoResp activityInfo = eduManager.getActivityInfo(eduConf.getActivityId(), proxyIp, proxyPort);
        if (!activityInfo.isBizSuccess()) {
            log.error("PJ-获取包信息失败: {}", JSON.toJSONString(activityInfo));
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "获取包信息失败");
        }
        ActivityDetail activityDetail = activityInfo.getExt();
        ActivityGood activityGood = activityDetail.getGoods().get(0);


        TelecomCheckResp telecomCheckResp = eduManager.checkIsTelecomWithResponse(mobile);
        if (!telecomCheckResp.isBizSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "该业务不支持这个电话号码");
        }

        EduSmsCodeReq req = new EduSmsCodeReq();
        req.setPhone(mobile);
        req.setApp_id(eduConf.getAppId());
        req.setSale_product_id(activityGood.getSaleProductId());
        req.setSource_id(activityDetail.getActivityId());
        req.setProvince_pay_type("");

        // 使用代理IP发送短信验证码
        EduSmsCodeResp eduSmsCodeResp = eduManager.sendSmsCode(req, proxyIp, proxyPort);
        if (!eduSmsCodeResp.isBizSuccess()) {
            log.error("PJ-获取验证码失败: {}", JSON.toJSONString(eduSmsCodeResp));
            throw BizException.create(BaseErrorCodeEnum.FALLBACK, "获取验证码失败");
        }
        eduCacheKey(mobile, activityInfo);
        return true;
    }

    private void eduCacheKey(String mobile, ActivityInfoResp activityInfoResp) {
        String cacheKey = RedisKeys.getBenefitNumber(getChannel(), mobile);
        String cacheVal = JSONObject.toJSONString(activityInfoResp);
        // 短信验证码是 5 分钟失效这边设置8 分钟
        RedisUtils.setCacheObject(cacheKey, cacheVal, Duration.ofMinutes(8));
    }

    private ActivityInfoResp getEduCacheKey(String mobile) {
        String cacheKey = RedisKeys.getBenefitNumber(getChannel(), mobile);
        Object cacheObject = RedisUtils.getCacheObject(cacheKey);
        if (Objects.isNull(cacheObject)) {
            return null;
        }
        return JSONObject.parseObject(cacheObject.toString(), ActivityInfoResp.class);
    }

    public BenefitPayResultBO verifyEduSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        HuBeiShuKePayPkgEnum payPkgEnum = HuBeiShuKePayPkgEnum.of(productDO.getPayChannelPkgId());
        HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(payPkgEnum);
        String mobile = reqParams.getMobile();
        String smsCode = reqParams.getSmsCode();


        // 获取用户缓存的代理IP
        String[] proxyInfo = getCachedProxyForUser(mobile);
        if (proxyInfo == null) {
            return BenefitPayResultBO.fail("代理IP已过期，请重新获取验证码");
        }
        String proxyIp = proxyInfo[0];
        int proxyPort = Integer.parseInt(proxyInfo[1]);

        ActivityInfoResp activityInfoResp = getEduCacheKey(mobile);
        if (Objects.isNull(activityInfoResp)) {
            return BenefitPayResultBO.fail("验证码已失效，请重新获取验证码");
        }
        ActivityDetail activityDetail = activityInfoResp.getExt();
        ActivityGood activityGood = activityDetail.getGoods().get(0);

        EduOrderConfirmReq confirmReq = new EduOrderConfirmReq();
        confirmReq.setPhone(eduManager.confirmOrderEncryptPhone(mobile));
        confirmReq.setApp_id(eduConf.getAppId());
        confirmReq.setSource_id(activityDetail.getActivityId());
        confirmReq.setSource_name(activityDetail.getActivityName());
        confirmReq.setSale_product_id(activityGood.getSaleProductId());
        confirmReq.setUa(HuBeiShuKeEduManager.USER_AGENT);
        confirmReq.setDevice_info(HuBeiShuKeEduManager.DEVICE_INFO);
        confirmReq.setSms_code(smsCode);
        confirmReq.setTime_stamp(System.currentTimeMillis());
        confirmReq.setCp_channel_code(eduConf.getCpChannelCode());
        confirmReq.setApp_name("");
        confirmReq.setProvince_pay_type("");

        // 使用代理IP确认订单
        EduOrderConfirmResp eduOrderConfirmResp = eduManager.confirmOrder(confirmReq, proxyIp, proxyPort);
        if (!eduOrderConfirmResp.isSuccess()) {
            return BenefitPayResultBO.fail("订购失败");
        }

        // 没有订单号 只有这个 tw 值要把对应关系上报给数科 之后才会回调给我 否则他们无法区分 因为他们拿不到真手机号码
        orderDO.setExtraData(eduOrderConfirmResp.getExt());
        sendPhone2TwValue(orderDO.getOrderNo(), mobile, eduOrderConfirmResp.getExt());

        // 订单确认成功后，清除用户的代理IP缓存
        clearProxyForUser(mobile);

        return BenefitPayResultBO.success();
    }

    public void sendPhone2TwValue(String orderNo, String mobile, String twValue) {
        try {

            ShuKeTwReportReq req = new ShuKeTwReportReq();
            req.setClientOrderId(twValue);
            req.setMobile(mobile);
            JSONObject json = JSONObject.from(req);

            String body = encryptByRsa(json.toJSONString(), HuBeiShuKeEduProperties.RSA_PUBLIC_KEY);
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "text/plain; charset=utf-8");
            headers.put("source", HuBeiShuKeEduProperties.SOURCE);

            HttpTaskRequest httpTaskRequest = HttpTaskRequest.builder()
                    .url(HuBeiShuKeEduProperties.PHONE_TW_REPORT_URL)
                    .method("POST")
                    .headers(headers)
                    .body(body)
                    .sourceSystem("SHUKE_EDU")
                    .businessType(BUSINESS_TYPE)
                    .businessId(orderNo)
                    .maxRetryCount(6)
                    .retryInterval(600)
                    .successStrategy(SuccessStrategyEnum.RESPONSE_SHU_KE_SPECIAL)
                    .extData(json.toJSONString())
                    .build();
            httpAsyncTaskService.createTask(httpTaskRequest);

        } catch (Exception e) {
            log.error("上报 TW任务失败 phone: {}, tw: {}", mobile, twValue, e);
        }
    }

    public void eduPayNotify(String orderNo, ShuKeTwReportReq req) {
        try {
            BenefitOrderDO dbOrderDo = benefitPlatformService.getByOrderByOrderNo(orderNo);

            BenefitOrderDO updateDO = new BenefitOrderDO();
            updateDO.setOrderId(dbOrderDo.getOrderId());
            updateDO.setPayNotifyTime(LocalDateTime.now());
            dbOrderDo.setPayNotifyTime(LocalDateTime.now());

            updateDO.setPayNotifyContent(JSONObject.toJSONString(req));

            updateDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            dbOrderDo.setPayStatus(PayStatusEnum.SUCCESS.getCode());

            SpringUtil.getBean(BenefitOrderDOService.class)
                    .updateById(updateDO);

            benefitPlatformService.notifyMediaOrderResult(dbOrderDo, "success");

        } catch (Exception e) {
            log.error("数科教育回调失败", e);
        }
    }


    public void dealSuccessReport(HttpAsyncTask task) {
        log.info("查询到了 tw 支成功的orderId:{}订单：{}", task.getBusinessId(), JSONObject.parseObject(task.getExtData()));
        String extData = task.getExtData();
        ShuKeTwReportReq req = JSONObject.parseObject(extData, ShuKeTwReportReq.class);
        eduPayNotify(task.getBusinessId(), req);
    }
}
