package com.yuelan.hermes.quanyi.common.pojo.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 快代理配置类
 * 购买新订单 记得增加白名单配置
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Data
@Component
@ConfigurationProperties(prefix = "kuai-proxy")
public class KuaiProxyProperties {

    /**
     * 订单的-秘钥ID
     */
    private String secretId = "ohn1s7izeifzqqebesu4";

    /**
     * 订单的-签名
     */
    private String signature = "j81iwgg3192lsj8xmf8lqaswt0jvcf2a";

    /**
     * 代理用户名
     */
    private String proxyUser = "d2139373719";

    /**
     * 代理密码
     */
    private String proxyPass = "eiug7un9";

    /**
     * 测试页面URL
     */
    private String pageUrl = "https://dev.kdlapi.com/testproxy";

    /**
     * 获取代理IP的URL模板
     */
    private String urlTemplate = "https://dps.kdlapi.com/api/getdps/?secret_id=%s&signature=%s&num=1&format=text&sep=1,";

    /**
     * 获取完整的代理IP获取URL
     */
    public String getProxyUrl() {
        return String.format(urlTemplate, secretId, signature);
    }
}
