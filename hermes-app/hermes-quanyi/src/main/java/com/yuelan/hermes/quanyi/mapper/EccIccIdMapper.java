package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.base.MPJBaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccIccIdDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ICCID数据访问层
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
public interface EccIccIdMapper extends MPJBaseMapper<EccIccIdDO> {

    /**
     * 批量查询ICCID是否存在
     *
     * @param iccIdList ICCID列表
     * @return 已存在的ICCID记录
     */
    default List<EccIccIdDO> selectBatchByIccId(List<String> iccIdList) {
        return selectList(Wrappers.lambdaQuery(EccIccIdDO.class)
                .in(EccIccIdDO::getIccId, iccIdList));
    }


    /**
     * 检查ICCID是否存在
     *
     * @param iccId ICCID号码
     * @return 是否存在
     */
    default boolean existsByIccId(String iccId) {
        return selectCount(Wrappers.lambdaQuery(EccIccIdDO.class)
                .eq(EccIccIdDO::getIccId, iccId)) > 0;
    }

    void saveBatchIgnore(@Param("list") List<EccIccIdDO> saveList);

    default Long countByBatchId(Long batchId) {
        return selectCount(Wrappers.lambdaQuery(EccIccIdDO.class)
                .eq(EccIccIdDO::getBatchId, batchId));
    }
}
