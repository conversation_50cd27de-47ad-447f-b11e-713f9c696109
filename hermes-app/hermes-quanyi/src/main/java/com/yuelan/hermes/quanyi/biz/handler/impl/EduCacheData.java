package com.yuelan.hermes.quanyi.biz.handler.impl;

import com.yuelan.hermes.quanyi.remote.hbsk.response.ActivityInfoResp;
import lombok.Data;

/**
 * 教育包缓存数据
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Data
public class EduCacheData {

    /**
     * 活动信息响应
     */
    private ActivityInfoResp activityInfoResp;

    /**
     * 代理IP
     */
    private String proxyIp;

    /**
     * 代理端口
     */
    private int proxyPort;

    /**
     * 创建缓存数据
     * 
     * @param activityInfoResp 活动信息响应
     * @param proxyIp 代理IP
     * @param proxyPort 代理端口
     * @return 缓存数据
     */
    public static EduCacheData create(ActivityInfoResp activityInfoResp, String proxyIp, int proxyPort) {
        EduCacheData cacheData = new EduCacheData();
        cacheData.setActivityInfoResp(activityInfoResp);
        cacheData.setProxyIp(proxyIp);
        cacheData.setProxyPort(proxyPort);
        return cacheData;
    }

    /**
     * 检查是否有代理信息
     * 
     * @return 是否有代理信息
     */
    public boolean hasProxy() {
        return proxyIp != null && proxyPort > 0;
    }
}
