package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;

/**
 * <AUTHOR> 2024/12/25
 * @since 2024/12/25
 */
public interface EccNcOrderMapper extends BaseMapper<EccNcOrderDO> {

    default int getOuterChannelOrderCount(Long outChannelId, Integer channelType, String channelOrderNo) {
        return selectCount(Wrappers.<EccNcOrderDO>lambdaQuery()
                .eq(EccNcOrderDO::getChannelId, outChannelId)
                .eq(EccNcOrderDO::getChannelType, channelType)
                .eq(EccNcOrderDO::getChannelOrderNo, channelOrderNo)).intValue();
    }

}