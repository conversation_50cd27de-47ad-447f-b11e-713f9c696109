package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 湖北数科教育包回调通知请求
 *
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 */
@Data
public class HuBeiShuKeEduNotifyReq {

    /**
     * 响应码：0订单成功，非0为订单失败  要组合 订购类型判断
     */
    private Integer code;

    /**
     * 订购成功或者失败字符描述，如SUCCESS、FALSE
     */
    private String text;

    /**
     * 扩展信息
     */
    private HuBeiShuKeEduNotifyExt ext;

    /**
     * 判断订单是否成功
     */
    @JSONField(serialize = false)
    public boolean isSuccessStatusNotify() {
        return code != null && code == 0;
    }

    /**
     * 判断是否状态成功
     */
    @JSONField(serialize = false)
    public boolean isSubscribeNotify() {
        return ext != null && ext.isSubscribe();
    }

    /**
     * 判断是否为退订通知
     */
    @JSONField(serialize = false)
    public boolean isUnsubscribeNotify() {
        return ext != null && ext.isUnsubscribe();
    }


}
