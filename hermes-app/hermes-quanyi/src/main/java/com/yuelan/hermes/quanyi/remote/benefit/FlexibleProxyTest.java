package com.yuelan.hermes.quanyi.remote.benefit;

import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.common.proxy.KuaiProxyHttpRequestSetter;
import com.yuelan.hermes.quanyi.common.proxy.ProxyHttpRequestSetter;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.ActivityDetail;
import com.yuelan.hermes.quanyi.remote.hbsk.response.ActivityGood;
import com.yuelan.hermes.quanyi.remote.hbsk.response.ActivityInfoResp;
import com.yuelan.hermes.quanyi.remote.hbsk.response.EduSmsCodeResp;
import com.yuelan.hermes.quanyi.remote.kuaiProxy.KuaiProxyManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 灵活代理测试类
 * 展示如何使用不同的代理服务
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FlexibleProxyTest {

    private final HuBeiShuKeEduManager eduManager;
    private final KuaiProxyManager kuaiProxyManager;

    /**
     * 测试使用快代理
     */
    public void testWithKuaiProxy(String mobile) {
        try {
            log.info("=== 测试使用快代理 ===");
            
            // 获取快代理IP
            String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort();
            String[] parts = proxyIpAndPort.split(":");
            String proxyIp = parts[0].trim();
            int proxyPort = Integer.parseInt(parts[1].trim());
            
            // 创建快代理设置器
            ProxyHttpRequestSetter proxySetter = KuaiProxyHttpRequestSetter.create(kuaiProxyManager, proxyIp, proxyPort);
            
            // 使用快代理进行业务操作
            testEduFlow(mobile, proxySetter, "快代理");
            
        } catch (Exception e) {
            log.error("测试快代理失败", e);
        }
    }

    /**
     * 测试使用自定义代理
     */
    public void testWithCustomProxy(String mobile) {
        try {
            log.info("=== 测试使用自定义代理 ===");
            
            // 假设这是从其他代理服务获取的IP和端口
            String customProxyIp = "*************";
            int customProxyPort = 8080;
            
            // 创建自定义代理设置器
            ProxyHttpRequestSetter proxySetter = CustomProxyHttpRequestSetter.create(customProxyIp, customProxyPort);
            
            // 使用自定义代理进行业务操作
            testEduFlow(mobile, proxySetter, "自定义代理");
            
        } catch (Exception e) {
            log.error("测试自定义代理失败", e);
        }
    }

    /**
     * 测试使用带认证的自定义代理
     */
    public void testWithAuthCustomProxy(String mobile) {
        try {
            log.info("=== 测试使用带认证的自定义代理 ===");
            
            // 假设这是从其他代理服务获取的IP、端口和认证信息
            String customProxyIp = "*************";
            int customProxyPort = 8080;
            String username = "proxyuser";
            String password = "proxypass";
            
            // 创建带认证的自定义代理设置器
            ProxyHttpRequestSetter proxySetter = CustomProxyHttpRequestSetter.createWithAuth(
                customProxyIp, customProxyPort, username, password);
            
            // 使用带认证的自定义代理进行业务操作
            testEduFlow(mobile, proxySetter, "带认证的自定义代理");
            
        } catch (Exception e) {
            log.error("测试带认证的自定义代理失败", e);
        }
    }

    /**
     * 测试不使用代理
     */
    public void testWithoutProxy(String mobile) {
        try {
            log.info("=== 测试不使用代理 ===");
            
            // 不使用代理进行业务操作
            testEduFlow(mobile, null, "无代理");
            
        } catch (Exception e) {
            log.error("测试无代理失败", e);
        }
    }

    /**
     * 通用的教育包流程测试
     * 
     * @param mobile 手机号
     * @param proxySetter 代理设置器，可以为null
     * @param proxyType 代理类型描述
     */
    private void testEduFlow(String mobile, ProxyHttpRequestSetter proxySetter, String proxyType) {
        log.info("开始测试教育包流程 - 代理类型: {}", proxyType);
        
        try {
            // 获取教育包配置
            HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(HuBeiShuKePayPkgEnum.EDU_BY_THUMB);

            // 1. 获取活动信息
            ActivityInfoResp activityInfo = eduManager.getActivityInfo(eduConf.getActivityId(), proxySetter);
            if (!activityInfo.isBizSuccess()) {
                log.error("获取活动信息失败 - {}: {}", proxyType, activityInfo.getText());
                return;
            }
            log.info("获取活动信息成功 - {}", proxyType);

            ActivityDetail activityDetail = activityInfo.getExt();
            ActivityGood activityGood = activityDetail.getGoods().get(0);

            // 2. 检查手机号是否为电信用户
            var telecomCheckResp = eduManager.checkIsTelecomWithResponse(mobile, proxySetter);
            if (!telecomCheckResp.isBizSuccess()) {
                log.error("检查手机号失败 - {}: {}", proxyType, telecomCheckResp.getExt());
                return;
            }
            log.info("手机号检查通过 - {}", proxyType);

            // 3. 发送短信验证码
            EduSmsCodeReq smsReq = new EduSmsCodeReq();
            smsReq.setPhone(mobile);
            smsReq.setApp_id(eduConf.getAppId());
            smsReq.setSale_product_id(activityGood.getSaleProductId());
            smsReq.setSource_id(activityDetail.getActivityId());
            smsReq.setProvince_pay_type("");

            EduSmsCodeResp smsResp = eduManager.sendSmsCode(smsReq, proxySetter);
            if (!smsResp.isBizSuccess()) {
                log.error("发送短信验证码失败 - {}: {}", proxyType, smsResp.getText());
                return;
            }
            log.info("发送短信验证码成功 - {}", proxyType);

        } catch (Exception e) {
            log.error("测试教育包流程失败 - {}", proxyType, e);
        }
    }

    /**
     * 运行所有测试
     */
    public void runAllTests(String mobile) {
        log.info("开始运行所有代理测试...");
        
        testWithoutProxy(mobile);
        testWithKuaiProxy(mobile);
        testWithCustomProxy(mobile);
        testWithAuthCustomProxy(mobile);
        
        log.info("所有代理测试完成");
    }
}
