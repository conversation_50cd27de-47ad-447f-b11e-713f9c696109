package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.MerchantDO;
import com.yuelan.hermes.quanyi.controller.request.MerchantListReq;
import com.yuelan.hermes.quanyi.controller.response.MchRsp;
import com.yuelan.hermes.quanyi.controller.response.MerchantRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantMapper extends BaseMapper<MerchantDO> {
    List<MchRsp> search(@Param("keyword") String keyword);

    MerchantDO findByMchId(@Param("mchId") String mchId);

    MerchantDO findOne(@Param("merchantId") Long merchantId);

    List<MerchantDO> findAll();

    long countByMerchantListReq(MerchantListReq req);

    List<MerchantRsp> pageByMerchantListReq(MerchantListReq req);
}