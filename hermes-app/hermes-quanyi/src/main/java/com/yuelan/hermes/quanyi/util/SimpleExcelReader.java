package com.yuelan.hermes.quanyi.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 简单的Excel读取器
 * 直接输出每行的JSON格式数据
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
public class SimpleExcelReader {

    /**
     * 最简单的读取方法 - 直接输出JSON
     */
    public static void readAndPrintJson(String filePath) {
        log.info("开始读取Excel文件: {}", filePath);
        
        try {
            EasyExcel.read(filePath, new AnalysisEventListener<Map<Integer, String>>() {
                
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    int rowIndex = context.readRowHolder().getRowIndex();
                    
                    if (rowIndex == 0) {
                        // 打印表头信息
                        log.info("表头: {}", data);
                        return;
                    }
                    
                    // 创建JSON对象
                    JSONObject json = new JSONObject();
                    json.put("orderNo", getValue(data, 0));        // 订购页订单号
                    json.put("orderType", getValue(data, 1));      // 订购类型
                    json.put("status", getValue(data, 2));         // 状态
                    json.put("description", getValue(data, 3));    // 描述
                    json.put("mobile", getValue(data, 4));         // 手机号
                    json.put("rowIndex", rowIndex + 1);            // 行号
                    
                    // 直接输出JSON
                    System.out.println(json.toJSONString());
                }
                
                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("读取完成");
                }
                
                private String getValue(Map<Integer, String> data, int index) {
                    String value = data.get(index);
                    return value != null ? value.trim() : "";
                }
                
            }).sheet().doRead();
            
        } catch (Exception e) {
            log.error("读取Excel失败", e);
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        // 修改为您的Excel文件路径
        String filePath = "/Users/<USER>/Downloads/西米全量订单.xlsx";
        
        // 直接读取并输出JSON
        readAndPrintJson(filePath);
    }
}
