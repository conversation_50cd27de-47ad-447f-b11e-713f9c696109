package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.controller.request.EccAreaReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.00
 * @description: 省份数据Mapper，对应表"ecc_area"
 * @ClassName: EccAreaDOMapper
 * @Date 2024/5/6 20:38
 */
public interface EccAreaDOMapper extends BaseMapper<EccAreaDO> {

    /**
     * 获取省份信息列表，parent_id=0和level=1的数据
     *
     * @param operator 运营商编码
     */
    List<EccAreaDO> listProvince(@Param("operator") Integer operator);

    /**
     * 获取地区信息
     *
     * @param req      地区信息请求对象
     * @param operator 运营商编码
     */
    List<EccAreaDO> listArea(@Param("operator") Integer operator, @Param("req") EccAreaReq req);

    /**
     * 获取省份和城市信息
     */
    List<EccAreaDO> listProvinceAndCity(@Param("operator") Integer operator);

    /**
     * 返回这个运营商的省市区数量
     *
     * @param code 运营商编码
     * @return 省市区数量
     */
    default int countByOperator(Integer code) {
        return selectCount(Wrappers.<EccAreaDO>lambdaQuery().
                eq(EccAreaDO::getOperator, code)).intValue();
    }

    EccAreaDO getOneAreaByNumCode(@Param("operator") Integer operator, @Param("provinceCode") String provinceCode, @Param("cityCode") String cityCode);

    EccAreaDO getOneAreaByName(@Param("operator") Integer code, @Param("provinceName") String provinceName, @Param("cityName") String cityName);
}