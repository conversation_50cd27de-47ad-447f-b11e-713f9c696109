package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderDO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> 2024/5/13 下午3:50
 */
public interface EccOrderDOMapper extends BaseMapper<EccOrderDO> {
    default EccOrderDO selectByPhone(String phone) {
        return selectOne(Wrappers.<EccOrderDO>lambdaQuery()
                .eq(EccOrderDO::getPhone, phone));
    }

    default EccOrderDO selectByPhoneAndProdId(String phone, Long prodId) {
        return selectOne(Wrappers.<EccOrderDO>lambdaQuery()
                .eq(EccOrderDO::getPhone, phone)
                .eq(EccOrderDO::getProdId, prodId));
    }

    /**
     * 新建月表
     *
     * @param yyyyMM yyyyMM格式的月份
     */
    void createMonthTableIfNotExists(@Param("month") String yyyyMM);

    default void updateOrderStatus(Long orderId, Integer status) {
        update(Wrappers.<EccOrderDO>lambdaUpdate()
                .eq(EccOrderDO::getOrderId, orderId)
                .set(EccOrderDO::getOrderStatus, status));
    }

    /**
     * 减少剩余可领取次数 如果更新后的可领取次数等于0 更新状态为成功
     *
     * @param orderId 订单号
     */
    int reduceRemainAndSetStatus(@Param("orderId") Long orderId);

    /**
     * 判断是否包含某张表
     */
    String selectTableByTableName(@Param("tableName") String fullTableName);

    default int countByProdId(Long prodId) {
        return selectCount(Wrappers.<EccOrderDO>lambdaQuery()
                .eq(EccOrderDO::getProdId, prodId)).intValue();
    }
}