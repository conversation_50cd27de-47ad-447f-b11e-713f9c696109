package com.yuelan.hermes.quanyi.remote.kuaiProxy;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 快代理测试类
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KuaiProxyTest {

    private final KuaiProxyManager kuaiProxyManager;

    public static final String USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1";

    /**
     * 测试获取代理IP和端口
     */
    public void testGetProxyIpAndPort() {
        try {

            // 方法4：使用HTTP请求方式（默认获取1个）
            Map<String, Object> data = new HashMap<>();
            String proxyIpAndPort3 = kuaiProxyManager.getProxyIpAndPort(data);
            log.info("使用HTTP请求获取代理IP和端口（默认1个）: {}", proxyIpAndPort3);

            // 方法5：使用HTTP请求方式（获取5个）
            String proxyIpAndPort4 = kuaiProxyManager.getProxyIpAndPort(5, data);
            log.info("使用HTTP请求获取代理IP和端口（5个）: {}", proxyIpAndPort4);

        } catch (Exception e) {
            log.error("测试获取代理IP和端口失败", e);
        }
    }

    /**
     * 测试设置代理并发送请求
     */
    public void testSetProxyForHttpRequest() {
        try {
            // 获取代理IP和端口
            String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort(new HashMap<>());
            log.info("获取代理IP和端口: {}", proxyIpAndPort);

            String[] parts = proxyIpAndPort.split(":");
            if (parts.length >= 2) {
                String proxyIp = parts[0].trim();
                int proxyPort = Integer.parseInt(parts[1].trim());

                // 测试代理连接
                String testResult = kuaiProxyManager.testProxyConnection(proxyIp, proxyPort);
                log.info("代理连接测试结果: {}", testResult);

                // 使用代理发送业务请求
                String url = "https://api.play.cn/api/v1/promotional/activity/1034/info";
                log.info("获取活动信息请求地址: {}", url);

                HttpRequest httpRequest = HttpRequest.get(url)
                        .header(Header.ACCEPT, "application/json, text/plain, */*")
                        .header(Header.ORIGIN, "https://act.play.cn")
                        .header(Header.REFERER, "https://act.play.cn/")
                        .header(Header.USER_AGENT, USER_AGENT)
                        .header("User-Agent", USER_AGENT);

                // 为HttpRequest设置代理
                HttpRequest proxyRequest = kuaiProxyManager.setProxyForHttpRequest(proxyIp, proxyPort, httpRequest);

                HttpResponse response = proxyRequest.execute();
                String body = response.body();
                log.info("获取活动信息返回参数: {}", body);
            }

        } catch (Exception e) {
            log.error("测试设置代理并发送请求失败", e);
        }
    }

    /**
     * 主测试方法
     */
    public void runAllTests() {
        log.info("开始运行快代理测试...");
        
        testGetProxyIpAndPort();
        testSetProxyForHttpRequest();
        
        log.info("快代理测试完成");
    }
}
