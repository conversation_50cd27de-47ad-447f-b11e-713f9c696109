package com.yuelan.hermes.quanyi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.UserDO;

/**
 * <AUTHOR> 2024/7/29 上午11:32
 */
public interface UserDOMapper extends BaseMapper<UserDO> {
    default UserDO findByPhone(String phone) {
        return selectOne(Wrappers.<UserDO>lambdaQuery().eq(UserDO::getUserPhone, phone));
    }
}