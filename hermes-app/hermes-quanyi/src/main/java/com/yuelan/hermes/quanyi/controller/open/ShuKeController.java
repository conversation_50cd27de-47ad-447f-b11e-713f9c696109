package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHuBeiShuKePayChannel;
import com.yuelan.hermes.quanyi.controller.request.HuBeiShuKeEduNotifyReq;
import com.yuelan.hermes.quanyi.util.ExcelToJsonUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 * 对接
 * docs/数科教育/云游戏回调通知接口.docx
 */
@Slf4j
@Tag(name = "湖北数科")
@RequestMapping("/shuke")
@SaIgnore
@RestController
@RequiredArgsConstructor
public class ShuKeController {

    private final BenefitHuBeiShuKePayChannel benefitHuBeiShuKePayChannel;

    @PostMapping("/edu/notify")
    public String eduNotify(@RequestBody HuBeiShuKeEduNotifyReq req) {
        log.info("湖北数科教育包回调通知: {}", JSON.toJSONString(req));
        return benefitHuBeiShuKePayChannel.eduPayNotify(req);
    }

    /**
     * Excel数据模型类
     */
    @Data
    public static class ExcelOrderData {
        /**
         * 订购页订单号
         */
        private String orderNo;

        /**
         * 订购类型
         */
        private String orderType;

        /**
         * 状态
         */
        private String status;

        /**
         * 描述
         */
        private String description;

        /**
         * 手机号
         */
        private String mobile;
    }

    /**
     * 简化版本 - 使用工具类快速转换
     */
    public static void quickMain(String[] args) {
        String filePath = "/Users/<USER>/Downloads/西米全量订单.xlsx";

        log.info("=== 使用工具类快速转换 ===");
        ExcelToJsonUtil.quickConvert(filePath);
    }

    /**
     * 读取Excel文件并转换为JSON - 完整版本
     */
    public static void main(String[] args) {
        // Excel文件路径
        String filePath = "/Users/<USER>/Downloads/西米全量订单.xlsx";
        File file = new File(filePath);

        if (!file.exists()) {
            log.error("文件不存在: {}", filePath);
            return;
        }

        log.info("开始读取Excel文件: {}", filePath);

        // 存储所有行数据的列表
        List<JSONObject> jsonList = new ArrayList<>();

        try {
            // 使用EasyExcel读取文件
            EasyExcel.read(file, new AnalysisEventListener<Map<Integer, String>>() {

                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    // 跳过表头
                    if (context.readRowHolder().getRowIndex() == 0) {
                        log.info("表头数据: {}", rowData);
                        return;
                    }

                    // 将每行数据转换为JSON对象
                    JSONObject jsonObject = new JSONObject();

                    // 根据列索引映射到对应字段
                    jsonObject.put("orderNo", getValueByIndex(rowData, 0));        // 订购页订单号
                    jsonObject.put("orderType", getValueByIndex(rowData, 1));      // 订购类型
                    jsonObject.put("status", getValueByIndex(rowData, 2));         // 状态
                    jsonObject.put("description", getValueByIndex(rowData, 3));    // 描述
                    jsonObject.put("mobile", getValueByIndex(rowData, 4));         // 手机号

                    // 添加行号信息
                    jsonObject.put("rowIndex", context.readRowHolder().getRowIndex() + 1);

                    jsonList.add(jsonObject);

                    // 打印每行的JSON数据
                    log.info("第{}行数据: {}", context.readRowHolder().getRowIndex() + 1, jsonObject.toJSONString());
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共处理 {} 行数据", jsonList.size());

                    // 输出所有数据的JSON数组
                    log.info("所有数据JSON: {}", JSON.toJSONString(jsonList));

                    // 统计信息
                    printStatistics(jsonList);
                }

                /**
                 * 安全获取指定索引的值
                 */
                private String getValueByIndex(Map<Integer, String> rowData, int index) {
                    String value = rowData.get(index);
                    return value != null ? value.trim() : "";
                }

            }).sheet().doRead();

        } catch (Exception e) {
            log.error("读取Excel文件异常", e);
        }
    }

    /**
     * 打印统计信息
     */
    private static void printStatistics(List<JSONObject> jsonList) {
        log.info("=== 数据统计信息 ===");
        log.info("总行数: {}", jsonList.size());

        // 统计不同状态的数量
        Map<String, Long> statusCount = jsonList.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    json -> json.getString("status"),
                    java.util.stream.Collectors.counting()
                ));

        log.info("状态统计: {}", statusCount);

        // 统计不同订购类型的数量
        Map<String, Long> orderTypeCount = jsonList.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                    json -> json.getString("orderType"),
                    java.util.stream.Collectors.counting()
                ));

        log.info("订购类型统计: {}", orderTypeCount);

        // 统计手机号数量（去重）
        long uniqueMobileCount = jsonList.stream()
                .map(json -> json.getString("mobile"))
                .filter(mobile -> mobile != null && !mobile.isEmpty())
                .distinct()
                .count();

        log.info("唯一手机号数量: {}", uniqueMobileCount);
    }

    /**
     * 读取Excel文件并转换为JSON - 动态列映射版本
     * 支持根据表头自动映射列
     */
    public static void readExcelWithDynamicMapping(String filePath) {
        File file = new File(filePath);

        if (!file.exists()) {
            log.error("文件不存在: {}", filePath);
            return;
        }

        log.info("开始读取Excel文件（动态映射）: {}", filePath);

        // 存储表头映射
        final Map<Integer, String> headerMap = new java.util.HashMap<>();
        final List<JSONObject> jsonList = new ArrayList<>();

        try {
            EasyExcel.read(file, new AnalysisEventListener<Map<Integer, String>>() {

                @Override
                public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
                    int rowIndex = context.readRowHolder().getRowIndex();

                    if (rowIndex == 0) {
                        // 处理表头，建立列索引到字段名的映射
                        for (Map.Entry<Integer, String> entry : rowData.entrySet()) {
                            String headerName = entry.getValue();
                            if (headerName != null) {
                                headerName = headerName.trim();
                                // 映射中文表头到英文字段名
                                String fieldName = mapHeaderToField(headerName);
                                headerMap.put(entry.getKey(), fieldName);
                            }
                        }
                        log.info("表头映射: {}", headerMap);
                        return;
                    }

                    // 处理数据行
                    JSONObject jsonObject = new JSONObject();

                    for (Map.Entry<Integer, String> entry : rowData.entrySet()) {
                        Integer colIndex = entry.getKey();
                        String value = entry.getValue();
                        String fieldName = headerMap.get(colIndex);

                        if (fieldName != null) {
                            jsonObject.put(fieldName, value != null ? value.trim() : "");
                        }
                    }

                    // 添加行号信息
                    jsonObject.put("rowIndex", rowIndex + 1);

                    jsonList.add(jsonObject);

                    // 打印每行的JSON数据
                    log.info("第{}行数据: {}", rowIndex + 1, jsonObject.toJSONString());
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成（动态映射），共处理 {} 行数据", jsonList.size());
                    log.info("所有数据JSON: {}", JSON.toJSONString(jsonList));
                    printStatistics(jsonList);
                }

            }).sheet().doRead();

        } catch (Exception e) {
            log.error("读取Excel文件异常（动态映射）", e);
        }
    }

    /**
     * 映射中文表头到英文字段名
     */
    private static String mapHeaderToField(String headerName) {
        switch (headerName) {
            case "订购页订单号":
                return "orderNo";
            case "订购类型":
                return "orderType";
            case "状态":
                return "status";
            case "描述":
                return "description";
            case "手机号":
                return "mobile";
            default:
                // 如果没有匹配的映射，使用原始表头名
                return headerName.replaceAll("[^a-zA-Z0-9]", "");
        }
    }

    /**
     * 测试方法 - 可以在这里切换不同的读取方式
     */
    public static void testReadExcel() {
        String filePath = "/Users/<USER>/Downloads/西米全量订单.xlsx";

        log.info("=== 使用固定列映射读取 ===");
        main(new String[]{});

        log.info("=== 使用动态列映射读取 ===");
        readExcelWithDynamicMapping(filePath);
    }

}
}
