package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHuBeiShuKePayChannel;
import com.yuelan.hermes.quanyi.controller.request.HuBeiShuKeEduNotifyReq;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/8/11
 * @since 2025/8/11
 * 对接
 * docs/数科教育/云游戏回调通知接口.docx
 */
@Slf4j
@Tag(name = "湖北数科")
@RequestMapping("/shuke")
@SaIgnore
@RestController
@RequiredArgsConstructor
public class ShuKeController {

    private final BenefitHuBeiShuKePayChannel benefitHuBeiShuKePayChannel;

    @PostMapping("/edu/notify")
    public String eduNotify(HuBeiShuKeEduNotifyReq req) {
        log.info("湖北数科教育包回调通知: {}", JSON.toJSONString(req));
        return benefitHuBeiShuKePayChannel.eduPayNotify(req);
    }
}
