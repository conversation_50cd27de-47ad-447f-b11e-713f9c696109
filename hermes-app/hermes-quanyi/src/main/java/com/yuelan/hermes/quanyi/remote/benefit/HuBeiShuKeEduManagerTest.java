package com.yuelan.hermes.quanyi.remote.benefit;

import com.yuelan.hermes.quanyi.common.enums.HuBeiShuKePayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuBeiShuKeEduProperties;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduOrderConfirmReq;
import com.yuelan.hermes.quanyi.remote.hbsk.request.EduSmsCodeReq;
import com.yuelan.hermes.quanyi.remote.hbsk.response.*;
import com.yuelan.hermes.quanyi.remote.kuaiProxy.KuaiProxyManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 湖北数科教育包管理器测试类
 * 
 * <AUTHOR> 2025/8/13
 * @since 2025/8/13
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HuBeiShuKeEduManagerTest {

    private final HuBeiShuKeEduManager eduManager;
    private final KuaiProxyManager kuaiProxyManager;

    /**
     * 测试使用代理IP的完整流程
     */
    public void testEduFlowWithProxy(String mobile) {
        try {
            // 1. 获取代理IP
            String proxyIpAndPort = kuaiProxyManager.getProxyIpAndPort();
            log.info("获取代理IP: {}", proxyIpAndPort);
            
            String[] parts = proxyIpAndPort.split(":");
            String proxyIp = parts[0].trim();
            int proxyPort = Integer.parseInt(parts[1].trim());

            // 2. 获取教育包配置
            HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(HuBeiShuKePayPkgEnum.EDU_BY_THUMB);

            // 3. 使用代理IP获取活动信息
            ActivityInfoResp activityInfo = eduManager.getActivityInfo(eduConf.getActivityId(), proxyIp, proxyPort);
            if (!activityInfo.isBizSuccess()) {
                log.error("获取活动信息失败: {}", activityInfo.getText());
                return;
            }
            log.info("获取活动信息成功");

            ActivityDetail activityDetail = activityInfo.getExt();
            ActivityGood activityGood = activityDetail.getGoods().get(0);

            // 4. 检查手机号是否为电信用户
            TelecomCheckResp telecomCheckResp = eduManager.checkIsTelecomWithResponse(mobile);
            if (!telecomCheckResp.isBizSuccess()) {
                log.error("检查手机号是否为电信用户失败: {}", telecomCheckResp.getExt());
                return;
            }
            log.info("手机号检查通过");

            // 5. 使用代理IP发送短信验证码
            EduSmsCodeReq smsReq = new EduSmsCodeReq();
            smsReq.setPhone(mobile);
            smsReq.setApp_id(eduConf.getAppId());
            smsReq.setSale_product_id(activityGood.getSaleProductId());
            smsReq.setSource_id(activityDetail.getActivityId());
            smsReq.setProvince_pay_type("");

            EduSmsCodeResp smsResp = eduManager.sendSmsCode(smsReq, proxyIp, proxyPort);
            if (!smsResp.isBizSuccess()) {
                log.error("发送短信验证码失败: {}", smsResp.getText());
                return;
            }
            log.info("发送短信验证码成功");

            // 6. 模拟用户输入验证码（实际使用中需要用户输入）
            log.info("请输入收到的短信验证码...");
            String smsCode = "123456"; // 这里应该是用户输入的验证码

            // 7. 使用代理IP确认订单
            EduOrderConfirmReq confirmReq = new EduOrderConfirmReq();
            confirmReq.setPhone(eduManager.confirmOrderEncryptPhone(mobile));
            confirmReq.setApp_id(eduConf.getAppId());
            confirmReq.setSource_id(activityDetail.getActivityId());
            confirmReq.setSource_name(activityDetail.getActivityName());
            confirmReq.setSale_product_id(activityGood.getSaleProductId());
            confirmReq.setUa(HuBeiShuKeEduManager.USER_AGENT);
            confirmReq.setDevice_info(HuBeiShuKeEduManager.DEVICE_INFO);
            confirmReq.setSms_code(smsCode);
            confirmReq.setTime_stamp(System.currentTimeMillis());
            confirmReq.setCp_channel_code(eduConf.getCpChannelCode());
            confirmReq.setApp_name("");
            confirmReq.setProvince_pay_type("");

            EduOrderConfirmResp confirmResp = eduManager.confirmOrder(confirmReq, proxyIp, proxyPort);
            if (!confirmResp.isSuccess()) {
                log.error("确认订单失败: {}", confirmResp.getText());
                return;
            }
            log.info("确认订单成功，TW值: {}", confirmResp.getExt());

        } catch (Exception e) {
            log.error("测试教育包流程失败", e);
        }
    }

    /**
     * 测试不使用代理IP的流程（对比）
     */
    public void testEduFlowWithoutProxy(String mobile) {
        try {
            // 获取教育包配置
            HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(HuBeiShuKePayPkgEnum.EDU_BY_THUMB);

            // 不使用代理IP获取活动信息
            ActivityInfoResp activityInfo = eduManager.getActivityInfo(eduConf.getActivityId());
            if (!activityInfo.isBizSuccess()) {
                log.error("获取活动信息失败: {}", activityInfo.getText());
                return;
            }
            log.info("获取活动信息成功（无代理）");

            ActivityDetail activityDetail = activityInfo.getExt();
            ActivityGood activityGood = activityDetail.getGoods().get(0);

            // 检查手机号是否为电信用户
            TelecomCheckResp telecomCheckResp = eduManager.checkIsTelecomWithResponse(mobile);
            if (!telecomCheckResp.isBizSuccess()) {
                log.error("检查手机号是否为电信用户失败: {}", telecomCheckResp.getExt());
                return;
            }

            // 不使用代理IP发送短信验证码
            EduSmsCodeReq smsReq = new EduSmsCodeReq();
            smsReq.setPhone(mobile);
            smsReq.setApp_id(eduConf.getAppId());
            smsReq.setSale_product_id(activityGood.getSaleProductId());
            smsReq.setSource_id(activityDetail.getActivityId());
            smsReq.setProvince_pay_type("");

            EduSmsCodeResp smsResp = eduManager.sendSmsCode(smsReq);
            if (!smsResp.isBizSuccess()) {
                log.error("发送短信验证码失败: {}", smsResp.getText());
                return;
            }
            log.info("发送短信验证码成功（无代理）");

        } catch (Exception e) {
            log.error("测试教育包流程失败（无代理）", e);
        }
    }

    /**
     * 测试代理IP的灵活性
     */
    public void testProxyFlexibility() {
        try {
            // 获取多个代理IP进行测试
            String[] proxies = kuaiProxyManager.getMultipleProxyIpAndPort(3);
            log.info("获取到{}个代理IP", proxies.length);

            for (int i = 0; i < proxies.length; i++) {
                String[] parts = proxies[i].split(":");
                String proxyIp = parts[0].trim();
                int proxyPort = Integer.parseInt(parts[1].trim());

                log.info("测试代理IP {} - {}:{}", i + 1, proxyIp, proxyPort);

                // 使用不同的代理IP获取活动信息
                HuBeiShuKeEduProperties.EduConf eduConf = HuBeiShuKeEduProperties.pkgConfigMap.get(HuBeiShuKePayPkgEnum.EDU_BY_THUMB);
                ActivityInfoResp activityInfo = eduManager.getActivityInfo(eduConf.getActivityId(), proxyIp, proxyPort);
                
                if (activityInfo.isBizSuccess()) {
                    log.info("代理IP {} 测试成功", i + 1);
                } else {
                    log.warn("代理IP {} 测试失败: {}", i + 1, activityInfo.getText());
                }
            }

        } catch (Exception e) {
            log.error("测试代理IP灵活性失败", e);
        }
    }
}
